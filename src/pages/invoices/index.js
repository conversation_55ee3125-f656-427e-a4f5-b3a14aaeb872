import React, { useState, useEffect, useRef, useCallback } from 'react'; // Added useCallback
import axios from 'axios';
import { parseISO, formatDistanceToNow, differenceInDays, format } from 'date-fns'; // Added format
import {
  CheckCircleIcon,
  XCircleIcon,
  InformationCircleIcon,
  DocumentMagnifyingGlassIcon,
  ArrowPathIcon,
  PencilSquareIcon, // Added for Edit button
  ArrowUturnLeftIcon, // Added for Cancel Edit button
  CheckIcon, // Added for Save Changes button
} from '@heroicons/react/24/outline';
import { LinkIcon } from '@heroicons/react/24/solid';
import StatusBadge from '../../components/StatusBadge';
import config from "../../../config.json"
import * as XLSX from 'xlsx';

const INVOICE_STATUS_ORDER = {
  'APPOVAL_PENDING': 0,
  'VERIFICATION_PENDING_ANCHOR': 1,
  'MORE_INFO_NEEDED_ANCHOR': 2,
  'VERIFIED_ANCHOR': 3,
  'REJECTED_ANCHOR': 4,
  'VERIFICATION_PENDING_LENDER': 5,
  'MORE_INFO_NEEDED_LENDER': 6,
  'ACCEPTED_LENDER': 7,
  'REJECTED_LENDER': 8,
  'READY_FOR_DISBURSAL': 9,
  'LOAN_IN_PROGRESS': 10,
  'DISBURSED': 11,
  'DEFAULT': 12,
  'LOAN_CANCELLED': 13,
  'WRITTEN_OFF_PAID': 14,
  'UNKNOWN': 99, // Fallback for any other status
};
const DEFAULT_INVOICE_STATUS_PRIORITY = Math.max(...Object.values(INVOICE_STATUS_ORDER)) + 1;

// --- Loader Component ---
const Loader = ({ message = "Loading..." }) => ( // Added optional message prop
  <div className="flex items-center justify-center py-10">
    <div className="w-10 h-10 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
    <p className="ml-3 text-gray-600">{message}</p>
  </div>
);

// --- Helper function for calculating invoice age ---
const calculateInvoiceAge = (dateString) => {
  if (!dateString) return 'N/A';
  try {
    const date = parseISO(dateString);
    return formatDistanceToNow(date, { addSuffix: true });
  } catch (error) {
    console.error("Error parsing date for age calculation:", dateString, error);
    return 'Invalid Date';
  }
};

// --- Constants & Status Definitions ---
const ANCHOR_STATUS_OPTIONS = {
  APPROVE_FOR_BUYER: 'VERIFICATION_PENDING_ANCHOR',
  VERIFY: 'VERIFIED_ANCHOR',
  REJECT: 'REJECTED_ANCHOR',
  MORE_INFO: 'MORE_INFO_NEEDED_ANCHOR',
};

const STATUS_STYLES = {
  'APPOVAL_PENDING': { bg: 'bg-yellow-100', text: 'text-yellow-800', border: 'border-yelow-300' }, // ADD THIS LINE
  'VERIFICATION_PENDING_ANCHOR': { bg: 'bg-yellow-100', text: 'text-yellow-800', border: 'border-yellow-300' },
  'VERIFIED_ANCHOR': { bg: 'bg-green-100', text: 'text-green-800', border: 'border-green-300' },
  'REJECTED_ANCHOR': { bg: 'bg-red-100', text: 'text-red-800', border: 'border-red-300' },
  'MORE_INFO_NEEDED_ANCHOR': { bg: 'bg-orange-100', text: 'text-orange-800', border: 'border-orange-300' },
  'VERIFICATION_PENDING_LENDER': { bg: 'bg-blue-100', text: 'text-blue-800', border: 'border-blue-300' },
  'ACCEPTED_LENDER': { bg: 'bg-sky-100', text: 'text-sky-800', border: 'border-sky-300' },
  'REJECTED_LENDER': { bg: 'bg-pink-100', text: 'text-pink-800', border: 'border-pink-300' },
  'MORE_INFO_NEEDED_LENDER': { bg: 'bg-amber-100', text: 'text-amber-800', border: 'border-amber-300' },
  'DISBURSED': { bg: 'bg-teal-100', text: 'text-teal-800', border: 'border-teal-300' },
  'LOAN_IN_PROGRESS': { bg: 'bg-purple-100', text: 'text-purple-800', border: 'border-purple-300' },
  'DEFAULT': { bg: 'bg-gray-100', text: 'text-gray-800', border: 'border-gray-300' }
  // Add other statuses from schema if needed for display consistency
  // e.g., 'READY_FOR_DISBURSAL', 'LOAN_CANCELLED', 'WRITTEN_OFF_PAID'
};

const STATUS_DISPLAY_NAMES = {
  'APPOVAL_PENDING': 'Approval Pending', // ADD THIS LINE
  'VERIFICATION_PENDING_ANCHOR': 'Pending Verification',
  'VERIFIED_ANCHOR': 'Verified by You',
  'REJECTED_ANCHOR': 'Rejected by You',
  'MORE_INFO_NEEDED_ANCHOR': 'More Info Requested',
  // Display names for other statuses (even if not directly actionable by anchor)
  'VERIFICATION_PENDING_LENDER': 'Pending Lender',
  'ACCEPTED_LENDER': 'Accepted by Lender',
  'REJECTED_LENDER': 'Rejected by Lender',
  'MORE_INFO_NEEDED_LENDER': 'Lender Needs Info',
  'DISBURSED': 'Disbursed',
  'LOAN_IN_PROGRESS': 'Loan In Progress',
  'DEFAULT': 'Default',
  'READY_FOR_DISBURSAL': 'Ready For Disbursal',
  'LOAN_CANCELLED': 'Loan Cancelled',
  'WRITTEN_OFF_PAID': 'Written Off (Paid)',
  'UNKNOWN': 'Unknown Status'
};

// --- Main Component (Anchor Invoice Verification) ---
const InvoiceApprovals = () => {
  // --- State ---
  const [isLoading, setIsLoading] = useState(false); // Loading for button status actions
  const [isFetching, setIsFetching] = useState(true); // Loading for initial table fetch
  const [isSavingModal, setIsSavingModal] = useState(false); // **NEW**: Loading for modal save action
  const [showPdfPreview, setShowPdfPreview] = useState(false);
  const [pdfUrl, setPdfUrl] = useState('');
  const [invoices, setInvoices] = useState([]);
  const [fetchError, setFetchError] = useState(null);
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  const [verificationComments, setVerificationComments] = useState('');
  const [modalFormData, setModalFormData] = useState(null); // **NEW**: State for editable data in modal
  const [isEditingModal, setIsEditingModal] = useState(false); // **NEW**: Toggle for modal edit mode

  const [customDateRange, setCustomDateRange] = useState({ startDate: '', endDate: '' });
  const [invoiceNumberFilter, setInvoiceNumberFilter] = useState('');
  const [supplierNameFilter, setSupplierNameFilter] = useState('');
  const [amountRangeFilter, setAmountRangeFilter] = useState({ min: '', max: '' });
  const [useCustomDateRange, setUseCustomDateRange] = useState(false);
  const [supportTickets, setSupportTickets] = useState([]);
  const [relevantTicket, setRelevantTicket] = useState(null);
  // State for Upload Invoice functionality (preserved)
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [mindeeData, setMindeeData] = useState(null);
  const [extractingData, setExtractingData] = useState(false);
  const [isExportingExcel, setIsExportingExcel] = useState(false);

  // Filter State (preserved)
  const [statusFilter, setStatusFilter] = useState('');
  const [ageFilter, setAgeFilter] = useState('');
  const [filteredInvoices, setFilteredInvoices] = useState([]);

  // Refs (preserved)
  const fileInputRef = useRef(null);

  // --- Helper function for calculating invoice age --- (Already in your code)
  // const calculateInvoiceAge = (dateString) => { ... };

  // --- Helper function to format dates --- (Already in your code, renamed from modal's for clarity)
  const formatDateForExport = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      const date = parseISO(dateString);
      return format(date, 'dd-MMM-yyyy HH:mm'); // Example format, adjust as needed
    } catch (error) {
      console.error("Error parsing date for export formatting:", dateString, error);
      return 'Invalid Date';
    }
  };

  const getNested = (obj, path, defaultValue = undefined) => {
    const properties = path.split('.');
    return properties.reduce((acc, key) => (acc && acc[key] !== undefined && acc[key] !== null ? acc[key] : defaultValue), obj);
  };

  const addDocumentFieldsToRow = (rowData, docObject, prefix, getNestedFn, formatDateFnForExport) => {
    if (!docObject) {
      rowData[`${prefix} - File Name`] = 'N/A';
      rowData[`${prefix} - Signed URL`] = 'N/A';
      rowData[`${prefix} - Uploaded On`] = 'N/A';
      // Add other relevant doc fields as 'N/A' if the structure expects them
      rowData[`${prefix} - Status (if applicable)`] = 'N/A';
      rowData[`${prefix} - Notes (if applicable)`] = 'N/A';
      rowData[`${prefix} - MIME Type`] = 'N/A';
      return;
    }
    const filePath = getNestedFn(docObject, 'filePath', '');
    rowData[`${prefix} - File Name`] = filePath ? (filePath.split('/').pop() || 'document') : 'N/A';
    rowData[`${prefix} - Signed URL`] = getNestedFn(docObject, 'signedUrl', 'N/A');
    rowData[`${prefix} - Uploaded On`] = formatDateFnForExport(getNestedFn(docObject, 'uploadedOn'));
    // If documents have their own status/notes in your schema, add them:
    rowData[`${prefix} - Status (if applicable)`] = getNestedFn(docObject, 'verificationStatus', 'N/A');
    rowData[`${prefix} - Notes (if applicable)`] = getNestedFn(docObject, 'verificationNotes', '');
    rowData[`${prefix} - MIME Type`] = getNestedFn(docObject, 'mimeType', 'N/A');
  };

  // --- Effects ---
  // Fetch on initial mount (preserved)
  useEffect(() => {
    fetchInvoices();
  }, []);

  useEffect(() => {
    const fetchSupportTickets = async () => {
      try {
        // Using the endpoint from your reference code
        const response = await axios.get(`${config.apiUrl}/ops/invoiceFinancing/support-tickets/all-with-details`);
        if (response.data && Array.isArray(response.data)) {
          setSupportTickets(response.data);
        }
      } catch (error) {
        console.error("Failed to fetch support tickets:", error);
      }
    };

    fetchSupportTickets();
  }, []); // Runs once on component mount

  // Filtering useEffect (preserved)
  useEffect(() => {
    console.log("Filtering effect running with advanced filters");
    let tempInvoices = [...invoices]; // Start with the raw list

    // Apply Status Filter
    if (statusFilter) {
      tempInvoices = tempInvoices.filter(inv => inv?._doc?.status === statusFilter);
      // console.log(`After status filter ('${statusFilter}'):`, tempInvoices.length);
    }

    // Apply Invoice Number Filter
    if (invoiceNumberFilter.trim()) {
      tempInvoices = tempInvoices.filter(inv =>
        inv?._doc?.invoiceNumber?.toLowerCase().includes(invoiceNumberFilter.toLowerCase())
      );
      // console.log(`After invoice number filter:`, tempInvoices.length);
    }

    // Apply Supplier Name Filter
    if (supplierNameFilter.trim()) {
      tempInvoices = tempInvoices.filter(inv =>
        inv?._doc?.supplierName?.toLowerCase().includes(supplierNameFilter.toLowerCase())
      );
      // console.log(`After supplier name filter:`, tempInvoices.length);
    }

    // Apply Amount Range Filter
    if (amountRangeFilter.min || amountRangeFilter.max) {
      tempInvoices = tempInvoices.filter(inv => {
        if (!inv?._doc?.totalAmount) return false;
        const amount = typeof inv._doc.totalAmount === 'string'
          ? Number(inv._doc.totalAmount.replace(/[^0-9.-]+/g, ""))
          : Number(inv._doc.totalAmount);
        if (isNaN(amount)) return false;
        const min = amountRangeFilter.min ? Number(amountRangeFilter.min) : Number.MIN_SAFE_INTEGER;
        const max = amountRangeFilter.max ? Number(amountRangeFilter.max) : Number.MAX_SAFE_INTEGER;
        return amount >= min && amount <= max;
      });
      // console.log(`After amount range filter:`, tempInvoices.length);
    }

    // Apply Date Filters
    if (useCustomDateRange && (customDateRange.startDate || customDateRange.endDate)) {
      const startDate = customDateRange.startDate ? new Date(customDateRange.startDate) : new Date(0);
      const endDate = customDateRange.endDate ? new Date(customDateRange.endDate + 'T23:59:59') : new Date();
      tempInvoices = tempInvoices.filter(inv => {
        const dateField = inv?._doc?.uploadedAt || inv?._doc?.insertedOn;
        if (!dateField) return false;
        try {
          const invoiceDate = new Date(dateField);
          return invoiceDate >= startDate && invoiceDate <= endDate;
        } catch (e) {
          console.log(e)
          return false;
        }
      });
      // console.log(`After custom date range filter:`, tempInvoices.length);
    }
    else if (ageFilter && !useCustomDateRange) {
      const now = new Date();
      tempInvoices = tempInvoices.filter(inv => {
        const dateField = inv?._doc?.uploadedAt || inv?._doc?.insertedOn;
        if (!dateField) return false;
        try {
          const invoiceDate = parseISO(dateField);
          const diffDays = differenceInDays(now, invoiceDate);
          switch (ageFilter) {
            case '7': return diffDays <= 7;
            case '30': return diffDays <= 30;
            case '90': return diffDays <= 90;
            case 'over90': return diffDays > 90;
            default: return true;
          }
        } catch (e) {
          console.log(e)
          return false;
        }
      });
      // console.log(`After age filter ('${ageFilter}'):`, tempInvoices.length);
    }

    setFilteredInvoices(tempInvoices);
  }, [invoices, statusFilter, ageFilter, invoiceNumberFilter, supplierNameFilter, amountRangeFilter, customDateRange, useCustomDateRange]);

  // --- Helper Functions ---

  // Fetch Invoices (preserved)
  const fetchInvoices = useCallback(async () => { // Wrap in useCallback if needed, though dependency array is empty
    console.log("[Anchor] Fetching invoices...");
    setIsFetching(true);
    setFetchError(null);
    try {
      const response = await axios.get(`${config.apiUrl}/ops/invoiceFinancing/fetchInvoices`);
      if (response.data && Array.isArray(response.data)) {
        const fetchedInvoices = response.data;
        const cleanedInvoices = fetchedInvoices
          .filter(inv => inv && (inv._doc || inv))
          .map(inv => ({ ...inv, _doc: inv._doc || inv }))
          .filter(inv => inv._doc && inv._doc._id && inv._doc.status);

        // --- MODIFIED SORT LOGIC ---
        cleanedInvoices.sort((a, b) => {
          const statusA = a._doc?.status;
          const statusB = b._doc?.status;

          const orderA = INVOICE_STATUS_ORDER[statusA] ?? DEFAULT_INVOICE_STATUS_PRIORITY;
          const orderB = INVOICE_STATUS_ORDER[statusB] ?? DEFAULT_INVOICE_STATUS_PRIORITY;

          if (orderA !== orderB) {
            return orderA - orderB;
          }

          const dateValueA = a._doc?.uploadedAt || a._doc?.insertedOn || 0;
          const dateValueB = b._doc?.uploadedAt || b._doc?.insertedOn || 0;

          const dateA = new Date(dateValueA);
          const dateB = new Date(dateValueB);

          const timeA = !isNaN(dateA.getTime()) ? dateA.getTime() : 0;
          const timeB = !isNaN(dateB.getTime()) ? dateB.getTime() : 0;

          return timeB - timeA; // Newest first
        });
        // --- END MODIFIED SORT LOGIC ---
        console.log(`[Anchor] Setting ${cleanedInvoices.length} cleaned and sorted invoices.`);
        setInvoices(cleanedInvoices);
      } else {
        console.warn('[Anchor] Unexpected API response format or no data:', response);
        setInvoices([]);
      }
    } catch (error) {
      console.error('[Anchor] Error fetching invoices:', error.response?.data || error.message || error);
      const errorMsg = error.response?.data?.message || error.message || "An unknown error occurred while fetching invoices.";
      setFetchError(errorMsg);
      setInvoices([]);
    } finally {
      setIsFetching(false);
    }
  }, []); // Empty dependency array means this useCallback wrapper is technically optional here

  // Gets display style (preserved)
  // const getStatusStyle = (status) => {
  //   const style = STATUS_STYLES[status] || STATUS_STYLES['DEFAULT'];
  //   // Return object instead of string for more flexibility if needed later
  //   return { bg: style.bg, text: style.text, border: style.border };
  // };

  // Gets display name (preserved)
  const getStatusDisplay = (status) => {
    return STATUS_DISPLAY_NAMES[status] || status?.replace(/_/g, ' ') || STATUS_DISPLAY_NAMES['UNKNOWN'];
  };

  // Formats currency (preserved)
  const formatAmount = (amount) => {
    if (amount === null || amount === undefined || amount === '') return 'N/A';
    if (typeof amount === 'string' && amount.startsWith('QAR')) { return amount; }
    const numAmount = Number(String(amount).replace(/[^0-9.-]+/g, ""));
    if (isNaN(numAmount)) { return 'Invalid'; }
    return `QAR ${numAmount.toLocaleString('en-QA', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  // Formats Date for display (or input)
  const formatDate = (dateString, inputFormat = false) => {
    if (!dateString) return inputFormat ? '' : 'N/A';
    try {
      const date = parseISO(dateString);
      return format(date, inputFormat ? 'yyyy-MM-dd' : 'dd-MMM-yyyy'); // e.g., 2023-10-26 or 26-Oct-2023
    } catch (error) {
      console.error("Error parsing date for formatting:", dateString, error);
      return inputFormat ? '' : 'Invalid Date';
    }
  };

  // Extracts filename (preserved)
  const getFilenameFromPath = (path) => {
    if (!path) return 'document';
    try {
      const url = new URL(path);
      const pathnameParts = url.pathname.split('/');
      return decodeURIComponent(pathnameParts[pathnameParts.length - 1] || 'document');
    } catch (e) {
      console.log(e)

      const pathParts = path.split(/[/\\]/);
      return decodeURIComponent(pathParts[pathParts.length - 1] || 'document');
    }
  };

  // --- Event Handlers ---

  // Opens the modal (MODIFIED to initialize edit state)
  const handleViewInvoice = (invoice) => {
    if (invoice && invoice._doc && invoice._doc.signedUrl) {
      console.log("[Anchor] Viewing invoice:", invoice._doc._id, "Status:", invoice._doc.status);
      setSelectedInvoice(invoice);
      setPdfUrl(invoice._doc.signedUrl);
      setVerificationComments(invoice._doc.verificationComments || '');
      // **NEW**: Initialize modal form data as a copy of invoice data
      setModalFormData({ ...invoice._doc }); // Create a copy for editing
      setIsEditingModal(false); // Start in view mode
      // Find a relevant support ticket for the selected invoice
      const ticket = supportTickets.find(t =>
        t.category === 'Invoice Financing' &&
        t.userId === invoice._doc.userId && // Correct: Check if the user is the same
        t.subject && t.subject.startsWith(invoice._doc.invoiceNumber) // Keep this check
      );
      setRelevantTicket(ticket || null);

      setShowPdfPreview(true);
      setShowPdfPreview(true);
      setMindeeData(null);
      setExtractingData(false);
      setUploadedFile(null);
    } else {
      console.error('[Anchor] Cannot view invoice - Missing _doc or signedUrl:', invoice);
      const errorMsg = "Could not load invoice document preview URL. Please check the invoice data.";
      setFetchError(errorMsg);
      alert(errorMsg);
    }
  };

  // Handles status updates (preserved)
  const handleStatusUpdate = async (newStatus) => {
    if (!selectedInvoice || !selectedInvoice._doc || !selectedInvoice._doc._id) {
      alert("Error: No invoice selected to update."); return;
    }
    if ((newStatus === ANCHOR_STATUS_OPTIONS.REJECT || newStatus === ANCHOR_STATUS_OPTIONS.MORE_INFO) && !verificationComments.trim()) {
      alert("Please provide comments when rejecting or requesting more information."); return;
    }

    console.log(`[Anchor] Updating invoice ${selectedInvoice._doc._id} to status ${newStatus} with comments: "${verificationComments}"`);
    setIsLoading(true);
    setFetchError(null);
    try {
      await axios.put(
        `${config.apiUrl}/ops/invoiceFinancing/updateInvoice/${selectedInvoice._doc._id}`,
        { status: newStatus, verificationComments: verificationComments }
      );
      alert(`Invoice status updated to ${getStatusDisplay(newStatus)} successfully!`);
      setShowPdfPreview(false);
      setSelectedInvoice(null);
      setVerificationComments('');
      setModalFormData(null); // Clear modal form data
      await fetchInvoices();
    } catch (error) {
      console.error('[Anchor] Error updating invoice status:', error.response?.data || error.message);
      const errorMsg = error.response?.data?.message || error.message || 'Failed to update invoice status.';
      setFetchError(errorMsg);
      alert(`Update Failed: ${errorMsg}`);
    } finally {
      setIsLoading(false);
    }
  };

  // **NEW**: Handles input changes within the modal's edit form
  const handleModalInputChange = (fieldName, value) => {
    setModalFormData(prev => ({ ...prev, [fieldName]: value }));
  };

  // **NEW**: Handles saving the edited invoice data
  const handleSaveChanges = async () => {
    if (!modalFormData || !selectedInvoice?._doc?._id) {
      alert("Error: No invoice data available to save.");
      return;
    }
    // Identify editable fields based on schema
    const editableFields = [
      'invoiceNumber', 'invoiceDate', 'dueDate', 'totalAmount',
      'supplierName', 'customerName', 'billingAddress', 'customerAddress',
      'gstin', 'panNo' // Add others if needed
    ];
    const updateData = {};
    editableFields.forEach(field => {
      // Only include fields that have actually changed from the original (optional optimization)
      // Or simply include all editable fields from the modalFormData
      if (modalFormData[field] !== undefined) { // Check if the key exists
        updateData[field] = modalFormData[field];
      }
    });

    // Add verificationComments if it's part of general edits (usually not, it's for status changes)
    // updateData.verificationComments = verificationComments; // Decide if comments should be saved here

    if (Object.keys(updateData).length === 0) {
      alert("No changes detected to save.");
      setIsEditingModal(false); // Switch back to view mode if no changes
      return;
    }

    console.log(`[Anchor] Saving changes for invoice ${selectedInvoice._doc._id}`, updateData);
    setIsSavingModal(true); // Show saving loader
    setFetchError(null);

    try {
      await axios.put(
        `${config.apiUrl}/ops/invoiceFinancing/updateInvoice/${selectedInvoice._doc._id}`,
        updateData
      );

      alert('Invoice updated successfully!');
      await fetchInvoices(); // Refresh the main list
      setShowPdfPreview(false); // Close modal on success
      setSelectedInvoice(null);
      setModalFormData(null);
      setIsEditingModal(false); // Ensure edit mode is off

    } catch (error) {
      console.error('[Anchor] Error saving invoice changes:', error.response?.data || error.message);
      const errorMsg = error.response?.data?.message || error.message || 'Failed to save changes.';
      setFetchError(errorMsg); // Set error state
      alert(`Save Failed: ${errorMsg}`);
    } finally {
      setIsSavingModal(false); // Hide saving loader
    }
  };

  // **NEW**: Handles cancelling the edit mode in the modal
  const handleCancelEdit = () => {
    setModalFormData({ ...selectedInvoice._doc }); // Reset form data to original
    setIsEditingModal(false); // Switch back to view mode
  };

  // Place this function inside your InvoiceApprovals component or in a scope where it can access helpers
  const flattenInvoiceData = (invoiceWrapper, maxAdditionalDocs, getNestedFn, formatDateFnForExport, calculateAgeFn, formatAmountFn, getStatusDisplayFn, addDocFn) => {
    const rowData = {};
    const invoice = invoiceWrapper._doc || invoiceWrapper; // Ensure working with core data

    // --- I. Data from Main Table View ---
    rowData['Invoice ID'] = getNestedFn(invoice, '_id', 'N/A');
    rowData['Invoice Number'] = getNestedFn(invoice, 'invoiceNumber', 'N/A');
    rowData['Invoice Date'] = formatDateFnForExport(getNestedFn(invoice, 'invoiceDate'));
    rowData['Due Date'] = formatDateFnForExport(getNestedFn(invoice, 'dueDate'));
    rowData['Invoice Age'] = calculateAgeFn(getNestedFn(invoice, 'uploadedAt') || getNestedFn(invoice, 'insertedOn'));
    rowData['Buyer (Supplier in Code)'] = getNestedFn(invoice, 'supplierName', 'N/A');
    rowData['Supplier (Customer in Code)'] = getNestedFn(invoice, 'customerName', 'N/A');
    rowData['Raw Total Amount'] = getNestedFn(invoice, 'totalAmount', null); // For numerical analysis
    rowData['Formatted Total Amount'] = formatAmountFn(getNestedFn(invoice, 'totalAmount'));
    rowData['Status (Raw)'] = getNestedFn(invoice, 'status', 'N/A');
    rowData['Status (Display)'] = getStatusDisplayFn(getNestedFn(invoice, 'status'));
    rowData['Uploaded At'] = formatDateFnForExport(getNestedFn(invoice, 'uploadedAt') || getNestedFn(invoice, 'insertedOn'));

    // --- II. Data from Modal (Invoice Details) ---
    rowData['GSTIN'] = getNestedFn(invoice, 'gstin', 'N/A');
    rowData['PAN'] = getNestedFn(invoice, 'panNo', 'N/A');
    rowData['Billing Address'] = getNestedFn(invoice, 'billingAddress', 'N/A');
    rowData['Supplier Address (Customer in Code)'] = getNestedFn(invoice, 'customerAddress', 'N/A');
    rowData['Verification Comments'] = getNestedFn(invoice, 'verificationComments', '');

    // --- III. Main Invoice Document ---
    // Create a pseudo document object for the main invoice file
    const mainInvoiceDocInfo = {
      filePath: getNestedFn(invoice, 'filePath', `Invoice_${getNestedFn(invoice, 'invoiceNumber', 'N/A')}.pdf`), // Fallback name
      signedUrl: getNestedFn(invoice, 'signedUrl'),
      uploadedOn: getNestedFn(invoice, 'uploadedAt') || getNestedFn(invoice, 'insertedOn'),
      verificationStatus: getNestedFn(invoice, 'status'), // Invoice status itself
      verificationNotes: getNestedFn(invoice, 'verificationComments'), // Comments on the invoice
      mimeType: getNestedFn(invoice, 'mimeType', 'application/pdf'),
    };
    addDocFn(rowData, mainInvoiceDocInfo, 'Main Invoice File', getNestedFn, formatDateFnForExport);

    // --- IV. Additional/Supporting Documents ---
    const additionalDocs = getNestedFn(invoice, 'additionalInvoiceDocuments', []) || [];
    for (let i = 0; i < maxAdditionalDocs; i++) {
      const doc = additionalDocs[i];
      const prefix = `Supporting Doc ${i + 1}`;
      if (doc) {
        addDocFn(rowData, doc, prefix, getNestedFn, formatDateFnForExport);
      } else {
        // Add blank fields for this additional doc index
        ['File Name', 'Signed URL', 'Uploaded On', 'Status (if applicable)', 'Notes (if applicable)', 'MIME Type'].forEach(field => rowData[`${prefix} - ${field}`] = '');
      }
    }

    // --- V. Other Relevant Invoice Fields ---
    rowData['Currency'] = getNestedFn(invoice, 'currency', 'N/A');
    rowData['Sub Total'] = getNestedFn(invoice, 'subTotal', 'N/A');
    rowData['Total Tax'] = getNestedFn(invoice, 'totalTax', 'N/A');
    rowData['Payment Terms'] = getNestedFn(invoice, 'paymentTerms', 'N/A');
    rowData['Lender ID (Financed By)'] = getNestedFn(invoice, 'lenderId', 'N/A');
    rowData['Anchor ID'] = getNestedFn(invoice, 'anchorId', 'N/A');
    rowData['Financing ID'] = getNestedFn(invoice, 'financingId', 'N/A');
    rowData['Created At (System)'] = formatDateFnForExport(getNestedFn(invoice, 'insertedOn')); // if different from uploadedAt
    rowData['Updated At (System)'] = formatDateFnForExport(getNestedFn(invoice, 'updatedAt'));

    // Mindee extracted fields (if available at invoice level)
    rowData['Mindee Extracted Invoice No'] = getNestedFn(invoice, 'mindeeData.invoiceNumber', 'N/A');
    rowData['Mindee Extracted Invoice Date'] = formatDateFnForExport(getNestedFn(invoice, 'mindeeData.invoiceDate'));
    rowData['Mindee Extracted Due Date'] = formatDateFnForExport(getNestedFn(invoice, 'mindeeData.dueDate'));
    rowData['Mindee Extracted Total Amount'] = getNestedFn(invoice, 'mindeeData.totalAmount', 'N/A');
    rowData['Mindee Extracted Supplier Name'] = getNestedFn(invoice, 'mindeeData.supplierName', 'N/A');
    rowData['Mindee Extracted Customer Name'] = getNestedFn(invoice, 'mindeeData.customerName', 'N/A');


    // Any fields used by filters, if not already covered:
    // For example, if `uploadedAt` vs `insertedOn` is crucial.

    return rowData;
  };

  // Place this function inside your InvoiceApprovals component
  const handleExportInvoices = async () => {
    const itemsToExport = filteredInvoices; // Exporting the currently filtered list

    if (!itemsToExport || itemsToExport.length === 0) {
      alert("No invoices in the current view to export.");
      return;
    }
    setIsExportingExcel(true);

    try {
      let maxAdditionalDocs = 0;
      itemsToExport.forEach(invoiceWrapper => {
        const invoice = invoiceWrapper._doc || invoiceWrapper;
        const additionalDocs = getNested(invoice, 'additionalInvoiceDocuments', []) || [];
        if (additionalDocs.length > maxAdditionalDocs) {
          maxAdditionalDocs = additionalDocs.length;
        }
      });
      if (maxAdditionalDocs === 0) maxAdditionalDocs = 1; // For header consistency

      const excelData = itemsToExport.map(invoiceWrapper =>
        flattenInvoiceData(invoiceWrapper, maxAdditionalDocs, getNested, formatDateForExport, calculateInvoiceAge, formatAmount, getStatusDisplay, addDocumentFieldsToRow)
      );

      if (excelData.length === 0) {
        alert("No data to export after processing.");
        setIsExportingExcel(false);
        return;
      }

      const worksheet = XLSX.utils.json_to_sheet(excelData);

      if (excelData.length > 0 && excelData[0]) {
        const headers = Object.keys(excelData[0]);
        const colWidths = headers.map(header => {
          const headerLength = header ? header.toString().length : 10;
          return { wch: headerLength + 2 }; // Padding of +2
        });
        worksheet['!cols'] = colWidths;
      }

      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "InvoiceApprovals");

      const now = new Date();
      const timestamp = `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}_${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}`;
      XLSX.writeFile(workbook, `Invoice_Approvals_Export_${timestamp}.xlsx`);

    } catch (error) {
      console.error("Error exporting invoice data to Excel:", error);
      alert("An error occurred while exporting invoice data. Please check the console.");
    } finally {
      setIsExportingExcel(false);
    }
  };

  // --- Upload Invoice Handlers (preserved) ---
  const handleFileUpload_UploadModal = async (e) => {
    const file = e.target.files[0];
    if (file) {
      setUploadedFile(file);
      setMindeeData(null);
      setExtractingData(true);
      try {
        const mindeeFormData = new FormData();
        mindeeFormData.append("document", file);
        const mindeeApiKey = "a22f7e4051a9178de0f37d3d7a49b17c"; // Use env var ideally
        const mindeeResponse = await axios.post(
          "https://api.mindee.net/v1/products/mindee/invoices/v4/predict",
          mindeeFormData,
          { headers: { Authorization: `Token ${mindeeApiKey}`, "Content-Type": "multipart/form-data" } }
        );
        const data = mindeeResponse.data.document.inference.prediction;
        setMindeeData({
          invoiceNumber: data.invoice_number?.value || '',
          invoiceDate: data.date?.value || '',
          dueDate: data.due_date?.value || '',
          totalAmount: data.total_amount?.value || '',
          supplierName: data.supplier_name?.value || '',
          customerName: data.customer_name?.value || '',
        });
      } catch (mindeeError) {
        console.error('Error with Mindee API during upload:', mindeeError);
        alert("Failed to extract data automatically.");
        setMindeeData(null);
      } finally {
        setExtractingData(false);
      }
    }
  };

  const handleUploadSubmit_UploadModal = async () => {
    if (!uploadedFile) { alert("Please select an invoice PDF."); return; }
    setIsLoading(true); setFetchError(null);
    try {
      const formData = new FormData();
      formData.append('pdfFile', uploadedFile);
      if (mindeeData) {
        Object.keys(mindeeData).forEach(key => {
          if (mindeeData[key] !== null && mindeeData[key] !== undefined) {
            formData.append(key, mindeeData[key]);
          }
        });
      }
      formData.append('status', "VERIFICATION_PENDING_ANCHOR");
      await axios.post(`${config.apiUrl}/ops/invoiceFinancing/uploadInvoice`, formData, {
        headers: { 'Content-Type': 'multipart/form-data', /* 'x-auth-token': token */ }
      });
      alert("Invoice uploaded successfully!");
      await fetchInvoices();
      setShowUploadModal(false); setUploadedFile(null); setMindeeData(null);
    } catch (error) {
      console.error('Error uploading invoice:', error.response?.data || error.message);
      const errorMsg = error.response?.data?.message || error.message || 'Failed to upload invoice.';
      setFetchError(errorMsg); alert(`Upload Failed: ${errorMsg}`);
    } finally { setIsLoading(false); }
  };

  // --- Render Functions ---

  // **MODIFIED**: Renders Detail Items (conditionally editable)
  const renderEditableDetailItem = (label, fieldName, type = 'text', isCurrency = false) => {
    // Get value based on mode
    const value = modalFormData ? modalFormData[fieldName] : 'N/A';

    // Format for display (only in view mode)
    let displayValue = value;
    if (!isEditingModal) {
      if (type === 'date') {
        displayValue = formatDate(value);
      } else if (isCurrency) {
        displayValue = formatAmount(value);
      } else {
        displayValue = value || 'N/A';
      }
    }

    return (
      <div className="bg-gray-50 rounded-md p-2 sm:p-2.5 border border-gray-100">
        <label htmlFor={`modal-${fieldName}`} className="text-sm font-medium text-gray-500 block mb-0.5">{label}</label>
        {isEditingModal ? (
          <input
            type={type === 'date' ? 'date' : type === 'number' ? 'number' : 'text'} // Use appropriate input type
            id={`modal-${fieldName}`}
            name={fieldName}
            value={type === 'date' ? formatDate(value, true) : value || ''} // Format date for input, handle null/undefined
            onChange={(e) => handleModalInputChange(fieldName, e.target.value)}
            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm py-1 px-1.5"
            step={isCurrency ? "0.01" : undefined} // Allow decimals for currency
          />
        ) : (
          <span className="text-sm sm:text-base text-gray-800 font-medium break-words">{displayValue}</span>
        )}
      </div>
    );
  };

  // **MODIFIED**: Renders the main modal content with editable fields
  const renderInvoiceDetailsModalContent = (invoice) => {
    // Use modalFormData for display/edit when available, else fallback (should always be available after opening)
    const invoiceData = modalFormData || invoice?._doc;

    if (!invoiceData) {
      return <div className="p-4 text-center text-red-600">Error: Could not load invoice details.</div>;
    }

    // Get current status style for display
    // const statusStyle = getStatusStyle(invoiceData.status);

    return (
      <>
        {/* Header */}
        <div className="relative mb-4 pb-3 border-b border-gray-200 flex justify-between items-center">
          <h3 className="text-base sm:text-lg font-semibold text-gray-800">
            {isEditingModal ? 'Edit Invoice Details' : 'Review Invoice'}
          </h3>
          {/* Edit Toggle Button - Show only in View Mode */}
          {!isEditingModal && (
            <button
              onClick={() => setIsEditingModal(true)}
              className="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-sm font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              title="Edit Invoice Details"
            >
              <PencilSquareIcon className="h-4 w-4 mr-1.5 text-gray-500" /> Edit
            </button>
          )}
        </div>

        {/* --- User Change Request Notification (New Position) --- */}
        {relevantTicket && (
          <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-800 p-3 rounded-lg shadow-md mb-4 w-full">
            <h4 className="font-bold text-sm flex items-center">
              <InformationCircleIcon className="h-5 w-5 mr-2 text-yellow-600" />
              User Change Request
            </h4>
            <p className="text-xs mt-1">
              A support ticket has been raised for this invoice, likely due to a data extraction error.
            </p>
            <p className="text-xs mt-2 truncate" title={relevantTicket.subject}>
              <strong>Subject:</strong> {relevantTicket.subject}
            </p>
            <p className="text-xs mt-1">
              Please review all fields carefully before verifying.
            </p>
          </div>
        )}

        {/* Details Grid - Uses renderEditableDetailItem */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-2 text-sm mb-3">
          {renderEditableDetailItem("Invoice Number", "invoiceNumber")}
          {renderEditableDetailItem("Buyer Name", "supplierName")} {/* Assuming supplierName holds Buyer */}
          {renderEditableDetailItem("Invoice Date", "invoiceDate", "date")}
          {renderEditableDetailItem("Trade/Legal Name", "customerName")} {/* Assuming customerName holds Supplier */}
          {renderEditableDetailItem("Due Date", "dueDate", "date")}
          {renderEditableDetailItem("GSTIN", "gstin")}
          {/* Add PAN if needed */}
          {/* {renderEditableDetailItem("PAN", "panNo")} */}
          {/* Address fields might need larger inputs (textarea) if editable */}
          {renderEditableDetailItem("Billing Address", "billingAddress")}
          {renderEditableDetailItem("Customer Address", "customerAddress")}

          {/* Display Current Status (Read-only) */}
          <div className="col-span-1 md:col-span-2 bg-gray-50 rounded-md p-2 sm:p-2.5 border border-gray-100">
            <span className="text-sm font-medium text-gray-500 block mb-0.5">Current Status</span>
            <StatusBadge status={invoiceData.status} />
          </div>
        </div>

        {/* Amount (Editable) */}
        <div className="mb-4">
          {renderEditableDetailItem("Total Amount Due", "totalAmount", "number", true)}
        </div>

        {/* Supporting Documents (Read-Only) */}
        {!isEditingModal && ( // Hide during edit mode for simplicity
          <div className="mt-3 pt-3 border-t border-gray-200">
            <h4 className="text-sm font-semibold text-gray-700 mb-2">Supporting Documents</h4>
            <div className="space-y-1.5 max-h-24 overflow-y-auto pr-1 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 text-xs border-t border-b py-2">
              {(invoiceData.additionalInvoiceDocuments && invoiceData.additionalInvoiceDocuments.length > 0) ? (
                invoiceData.additionalInvoiceDocuments.map((doc, index) => (
                  doc && (
                    <div key={doc._id || index} className="flex items-center justify-between bg-gray-100 p-1.5 pl-2 rounded border border-gray-200">
                      <div className="flex items-center space-x-1.5 overflow-hidden mr-2"><LinkIcon className="h-3.5 w-3.5 text-gray-500 flex-shrink-0" /><span className="truncate text-gray-700" title={getFilenameFromPath(doc.filePath)}>{getFilenameFromPath(doc.filePath)}</span></div>
                      {doc.signedUrl ? (<a href={doc.signedUrl} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline font-medium ml-2 flex-shrink-0 px-1.5 py-0.5 rounded hover:bg-blue-100" title={`View Document (Uploaded: ${doc.uploadedOn ? formatDate(doc.uploadedOn) : 'N/A'})`}>View</a>) : (<span className="text-gray-400 ml-2 flex-shrink-0 italic">No Link</span>)}
                    </div>
                  )
                ))
              ) : (<p className="text-gray-500 italic text-xs py-2">No supporting documents found.</p>)}
            </div>
          </div>
        )}

        {/* Verification Comments (Read-only in Edit mode, linked to status updates in View mode) */}
        <div className="mt-3 pt-3 border-t border-gray-200">
          <label htmlFor={`comments-${invoiceData._id}`} className="block text-sm font-medium text-gray-700 mb-1">
            {isEditingModal ? 'Verification Comments (Read Only)' : 'Add/Edit Comments for Status Change'}
          </label>
          <textarea
            id={`comments-${invoiceData._id}`}
            value={isEditingModal ? (modalFormData?.verificationComments || '') : verificationComments} // Show stored comment in edit, allow typing for status change in view
            onChange={(e) => !isEditingModal && setVerificationComments(e.target.value)} // Only allow change in view mode for status updates
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition text-sm disabled:bg-gray-100 disabled:cursor-not-allowed"
            rows={isEditingModal ? 2 : 3} // Shorter when read-only
            placeholder={isEditingModal ? 'Comments are added during status changes.' : "Add comments (required for rejection or requesting info)"}
            readOnly={isEditingModal} // Make read-only during edit mode
            disabled={isLoading || isSavingModal} // Disable if any action is loading
          />
        </div>
      </>
    );
  };

  // --- Main JSX Return ---
  return (
    <div className="p-4 sm:p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">

        <div className="bg-gradient-to-r from-slate-50 to-blue-50 rounded-xl p-6 mb-8 shadow-sm border border-slate-200/60">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            {/* Title Section */}
            <div className="flex items-center space-x-3">
              <div>
                <h1 className="text-2xl sm:text2xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">
                  Invoice Verification
                </h1>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-3">
              {/* Refresh Button */}
              <button
                onClick={fetchInvoices}
                disabled={isFetching || isLoading || isSavingModal || isExportingExcel}
                className={`
          group relative inline-flex items-center px-4 py-2.5 
          bg-white hover:bg-slate-50 
          border border-slate-200 hover:border-slate-300
          rounded-lg shadow-sm hover:shadow-md
          text-sm font-medium text-slate-700 hover:text-slate-900
          transition-all duration-200 ease-in-out
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
          ${(isFetching || isLoading || isSavingModal || isExportingExcel)
                    ? 'opacity-60 cursor-not-allowed'
                    : 'hover:-translate-y-0.5 active:translate-y-0'
                  }
        `}
              >
                <ArrowPathIcon className={`w-4 h-4 mr-2 transition-transform duration-200 ${isFetching ? 'animate-spin' : 'group-hover:rotate-180'
                  }`} />
                <span>Refresh</span>
                {/* Subtle glow effect on hover */}
                <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-blue-500/0 to-indigo-500/0 group-hover:from-blue-500/5 group-hover:to-indigo-500/5 transition-all duration-200"></div>
              </button>

              {/* Export Button */}
              {filteredInvoices && filteredInvoices.length > 0 && (
                <button
                  onClick={handleExportInvoices}
                  disabled={isExportingExcel || isFetching || isLoading || isSavingModal}
                  className={`
            group relative inline-flex items-center px-4 py-2.5
            bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700
            border border-transparent
            rounded-lg shadow-sm hover:shadow-lg
            text-sm font-medium text-white
            transition-all duration-200 ease-in-out
            focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2
            ${(isExportingExcel || isFetching || isLoading || isSavingModal)
                      ? 'opacity-60 cursor-not-allowed'
                      : 'hover:-translate-y-0.5 active:translate-y-0 hover:shadow-emerald-500/25'
                    }
          `}
                >
                  {isExportingExcel ? (
                    <>
                      <ArrowPathIcon className="w-4 h-4 mr-2 animate-spin" />
                      <span>Exporting...</span>
                    </>
                  ) : (
                    <>
                      <svg
                        className="w-4 h-4 mr-2 transition-transform duration-200 group-hover:scale-110"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                      <span>Export Data</span>
                    </>
                  )}
                  {/* Shine effect on hover */}
                  <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-white/0 via-white/20 to-white/0 opacity-0 group-hover:opacity-100 group-hover:animate-pulse transition-opacity duration-200"></div>
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Active Filters Indicators (Preserved) */}
        {/* ... JSX for displaying active filters ... */}
        {(statusFilter || ageFilter || invoiceNumberFilter || supplierNameFilter ||
          amountRangeFilter.min || amountRangeFilter.max ||
          (useCustomDateRange && (customDateRange.startDate || customDateRange.endDate))) && (
            <div className="mb-4 flex flex-wrap gap-2">
              <span className="text-sm text-gray-600">Active filters:</span>
              {statusFilter && (<span className="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">Status: {getStatusDisplay(statusFilter)}</span>)}
              {!useCustomDateRange && ageFilter && (<span className="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">Age: {ageFilter === 'over90' ? 'Over 90 days' : `Last ${ageFilter} days`}</span>)}
              {useCustomDateRange && (customDateRange.startDate || customDateRange.endDate) && (<span className="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">Date: {formatDate(customDateRange.startDate) || 'Any'} to {formatDate(customDateRange.endDate) || 'Any'}</span>)}
              {invoiceNumberFilter && (<span className="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">Inv #: {invoiceNumberFilter}</span>)}
              {supplierNameFilter && (<span className="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">Buyer: {supplierNameFilter}</span>)}
              {(amountRangeFilter.min || amountRangeFilter.max) && (<span className="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">Amount: {amountRangeFilter.min ? `Min ${formatAmount(amountRangeFilter.min)}` : ''} {amountRangeFilter.min && amountRangeFilter.max ? ' - ' : ''} {amountRangeFilter.max ? `Max ${formatAmount(amountRangeFilter.max)}` : ''}</span>)}
            </div>
          )}


        {/* Error Display (Preserved) */}
        {fetchError && (<div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6 shadow-sm" role="alert"><strong className="font-bold">Error: </strong><span className="block sm:inline">{fetchError}</span></div>)}

        {/* --- Enhanced Filter UI (Preserved) --- */}
        <div className="mb-5 p-4 bg-white rounded-md shadow border border-gray-200">
          {/* ... Filter inputs grid ... */}
          <div className="flex flex-col mb-4">
            <h3 className="text-base font-semibold text-gray-700 mb-2">Filter Invoices</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {/* Status Filter */}
              <div>
                <label htmlFor="statusFilterAnchor" className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select id="statusFilterAnchor" name="statusFilter" value={statusFilter} onChange={(e) => setStatusFilter(e.target.value)} className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#004141] focus:border-[#004141] text-sm">
                  <option value="">All Statuses</option>
                  {Object.entries(STATUS_DISPLAY_NAMES).filter(([key]) => STATUS_STYLES[key]).sort(([, a], [, b]) => a.localeCompare(b)).map(([key, displayName]) => (<option key={key} value={key}>{displayName}</option>))}
                </select>
              </div>
              {/* Invoice Number Filter */}
              <div>
                <label htmlFor="invoiceNumberFilter" className="block text-sm font-medium text-gray-700 mb-1">Invoice Number</label>
                <input type="text" id="invoiceNumberFilter" value={invoiceNumberFilter} onChange={(e) => setInvoiceNumberFilter(e.target.value)} placeholder="Search by invoice #" className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#004141] focus:border-[#004141] text-sm" />
              </div>
              {/* Supplier Name Filter */}
              <div>
                <label htmlFor="supplierNameFilter" className="block text-sm font-medium text-gray-700 mb-1">Buyer</label>
                <input type="text" id="supplierNameFilter" value={supplierNameFilter} onChange={(e) => setSupplierNameFilter(e.target.value)} placeholder="Search by Buyer" className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#004141] focus:border-[#004141] text-sm" />
              </div>
              {/* Date Filter Toggle */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Date Filter Type</label>
                <div className="flex items-center space-x-4 pt-1"> {/* Added pt-1 */}
                  <label className="inline-flex items-center"><input type="radio" checked={!useCustomDateRange} onChange={() => setUseCustomDateRange(false)} className="form-radio h-4 w-4 text-[#004141]" /> <span className="ml-2 text-sm text-gray-700">Predefined</span></label>
                  <label className="inline-flex items-center"><input type="radio" checked={useCustomDateRange} onChange={() => setUseCustomDateRange(true)} className="form-radio h-4 w-4 text-[#004141]" /> <span className="ml-2 text-sm text-gray-700">Custom Range</span></label>
                </div>
              </div>
            </div>
            {/* Conditional Date/Age and Amount Filters */}
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mt-3">
              {!useCustomDateRange && (<div><label htmlFor="ageFilterAnchor" className="block text-sm font-medium text-gray-700 mb-1">Invoice Age</label><select id="ageFilterAnchor" name="ageFilter" value={ageFilter} onChange={(e) => setAgeFilter(e.target.value)} className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#004141] focus:border-[#004141] text-sm"><option value="">Any Age</option><option value="7">Added Last 7 Days</option><option value="30">Added Last 30 Days</option><option value="90">Added Last 90 Days</option><option value="over90">Added Over 90 Days Ago</option></select></div>)}
              {useCustomDateRange && (<><div><label htmlFor="startDateFilter" className="block text-sm font-medium text-gray-700 mb-1">Start Date</label><input type="date" id="startDateFilter" value={customDateRange.startDate} onChange={(e) => setCustomDateRange({ ...customDateRange, startDate: e.target.value })} className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#004141] focus:border-[#004141] text-sm" /></div><div><label htmlFor="endDateFilter" className="block text-sm font-medium text-gray-700 mb-1">End Date</label><input type="date" id="endDateFilter" value={customDateRange.endDate} onChange={(e) => setCustomDateRange({ ...customDateRange, endDate: e.target.value })} className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#004141] focus:border-[#004141] text-sm" /></div></>)}
              {/* Amount Range Filters */}
              <div><label htmlFor="minAmountFilter" className="block text-sm font-medium text-gray-700 mb-1">Min Amount</label><input type="number" id="minAmountFilter" value={amountRangeFilter.min} onChange={(e) => setAmountRangeFilter({ ...amountRangeFilter, min: e.target.value })} placeholder="Minimum" className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#004141] focus:border-[#004141] text-sm" /></div>
              <div><label htmlFor="maxAmountFilter" className="block text-sm font-medium text-gray-700 mb-1">Max Amount</label><input type="number" id="maxAmountFilter" value={amountRangeFilter.max} onChange={(e) => setAmountRangeFilter({ ...amountRangeFilter, max: e.target.value })} placeholder="Maximum" className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#004141] focus:border-[#004141] text-sm" /></div>
            </div>
          </div>
          {/* Filter Actions */}
          <div className="flex justify-end items-center mt-4 border-t pt-4">
            <span className="text-sm text-gray-600 flex items-center mr-auto">
              {filteredInvoices.length} {filteredInvoices.length === 1 ? 'invoice' : 'invoices'} found
            </span>
            <button onClick={() => { setStatusFilter(''); setAgeFilter(''); setInvoiceNumberFilter(''); setSupplierNameFilter(''); setAmountRangeFilter({ min: '', max: '' }); setCustomDateRange({ startDate: '', endDate: '' }); setUseCustomDateRange(false); }} className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">Clear Filters</button>
          </div>
        </div>

        {/* Invoice Table (Preserved structure, uses filteredInvoices) */}
        <div className="bg-white shadow-md rounded-lg overflow-hidden border border-gray-200">
          <div className="overflow-x-auto">
            <table className="w-full min-w-[800px]">
              <thead className="bg-gray-100 border-b border-gray-200">
                <tr>
                  <th className="p-3 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">Invoice #</th>
                  <th className="p-3 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">Inv Date</th>
                  <th className="p-3 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">Due Date</th>
                  <th className="p-3 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">Age</th>
                  <th className="p-3 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">Buyer</th>
                  <th className="p-3 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">Trade/Legal Name</th>
                  <th className="p-3 text-right text-sm font-semibold text-gray-600 uppercase tracking-wider">Amount</th>
                  <th className="p-3 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">Status</th>
                  <th className="p-3 text-center text-sm font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {isFetching ? (
                  <tr><td colSpan="9" className="p-4 text-center"><Loader /></td></tr>
                ) : filteredInvoices.length === 0 ? (
                  <tr><td colSpan="9" className="p-6 text-center text-sm text-gray-500">{(statusFilter || ageFilter || invoiceNumberFilter || supplierNameFilter || amountRangeFilter.min || amountRangeFilter.max || customDateRange.startDate || customDateRange.endDate) ? 'No invoices match filters.' : 'No invoices found.'}</td></tr>
                ) : (
                  filteredInvoices.map((invoice) => (
                    (invoice && invoice._doc) && (
                      <tr key={invoice._doc._id} className="hover:bg-gray-50 transition-colors">
                        <td className="p-3 align-top whitespace-nowrap">
                          <button onClick={() => handleViewInvoice(invoice)} className="text-blue-600 hover:text-blue-800 hover:underline font-medium text-sm" title={`View details for ${invoice._doc.invoiceNumber}`}>{invoice._doc.invoiceNumber || 'N/A'}</button>
                        </td>
                        <td className="p-3 align-top text-sm text-gray-600 whitespace-nowrap">{formatDate(invoice._doc.invoiceDate)}</td>
                        <td className="p-3 align-top text-sm text-gray-600 whitespace-nowrap">{formatDate(invoice._doc.dueDate)}</td>
                        <td className="p-3 align-top text-sm text-gray-600 whitespace-nowrap">{calculateInvoiceAge(invoice._doc.uploadedAt || invoice._doc.insertedOn)}</td>
                        <td className="p-3 align-top text-sm text-gray-700 max-w-[150px] truncate" title={invoice._doc.supplierName}>{invoice._doc.supplierName || 'N/A'}</td>
                        <td className="p-3 align-top text-sm text-gray-700 max-w-[150px] truncate" title={invoice._doc.customerName}>{invoice._doc.customerName || 'N/A'}</td>
                        <td className="p-3 align-top text-sm text-gray-900 font-semibold whitespace-nowrap text-right">{formatAmount(invoice._doc.totalAmount)}</td>
                        <td className="p-3 align-top text-xs whitespace-nowrap">
                          <StatusBadge status={invoice._doc.status} />
                        </td>
                        <td className="p-3 align-top text-center whitespace-nowrap">
                          <button onClick={() => handleViewInvoice(invoice)} className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" title="View Details & Actions">
                            <DocumentMagnifyingGlassIcon className="h-4 w-4 mr-1.5 text-gray-500" /> Details
                          </button>
                        </td>
                      </tr>
                    )
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* --- Upload Modal (preserved) --- */}
        {showUploadModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 p-4">
            {/* ... Upload modal content ... */}
            <div className="bg-white p-6 rounded-lg shadow-lg w-full max-w-lg">
              <h3 className="text-xl font-semibold mb-4">Upload Invoice</h3>
              <div className="space-y-4">
                <input type="file" ref={fileInputRef} className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100" accept=".pdf" onChange={handleFileUpload_UploadModal} />
                {uploadedFile && !extractingData && (<p className="text-sm text-gray-600">Selected: {uploadedFile.name}</p>)}
                {extractingData && (<div className="flex items-center text-sm text-gray-600"><Loader message="Extracting..." /></div>)}
                <div className="flex justify-end space-x-3 pt-3">
                  <button onClick={() => { setShowUploadModal(false); setUploadedFile(null); setMindeeData(null); setExtractingData(false); }} className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 text-sm font-medium">Cancel</button>
                  <button onClick={handleUploadSubmit_UploadModal} className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm font-medium disabled:opacity-50" disabled={!uploadedFile || extractingData || isLoading}> {isLoading ? 'Submitting...' : (extractingData ? 'Processing...' : 'Upload & Submit')} </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* --- Details/Action Modal (MODIFIED) --- */}
        {showPdfPreview && selectedInvoice && selectedInvoice._doc && (
          <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-[60] p-4 overflow-y-auto">
            <div className="bg-white rounded-xl shadow-2xl w-full max-w-6xl h-full max-h-[95vh] flex flex-col overflow-hidden">

              {/* Modal Header (preserved) */}
              <div className="flex justify-between items-center px-5 py-4 border-b border-gray-200">
                <h3 className="text-lg sm:text-xl font-semibold text-gray-800">
                  Invoice Details: {selectedInvoice._doc.invoiceNumber || 'N/A'}
                </h3>
                <button
                  onClick={() => {
                    setShowPdfPreview(false); setPdfUrl(''); setSelectedInvoice(null);
                    setVerificationComments(''); setModalFormData(null); setIsEditingModal(false);
                    setRelevantTicket(null);
                  }}
                  className="text-gray-500 hover:text-gray-800 p-1 rounded-full hover:bg-gray-100 transition-colors"
                  aria-label="Close"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" /></svg>
                </button>
              </div>

              {/* Modal Body (preserved structure, content function modified) */}
              <div className="flex-grow flex flex-col md:flex-row gap-4 overflow-hidden p-4">
                {/* Left: PDF Viewer (preserved) */}
                <div className="w-full md:w-3/5 h-full border border-gray-200 rounded-lg overflow-hidden flex flex-col bg-gray-100">
                  <div className="p-2 bg-gray-200 border-b border-gray-300"><p className="text-sm font-medium text-gray-700">Invoice Document</p></div>
                  {pdfUrl ? (<iframe src={pdfUrl} className="w-full h-full border-0 min-h-[400px]" title="Invoice PDF Preview" />) : (<div className="flex items-center justify-center flex-grow text-gray-500 p-10">Preview not available.</div>)}
                </div>

                {/* Right: Details Pane (uses modified function) */}
                <div className="w-full md:w-2/5 flex flex-col h-full">
                  <div className="flex-grow overflow-y-auto pr-1 scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-100">
                    {/* Render editable content */}
                    {renderInvoiceDetailsModalContent(selectedInvoice)}
                  </div>

                  {/* --- MODIFIED: Sticky Action Buttons Footer --- */}
                  <div className="sticky bottom-0 bg-white border-t border-gray-200 mt-2 pt-4 p-3
                grid grid-cols-2 gap-2      /* This is the key change: always 2 columns (except tiny screens) */
                sm:gap-3                     /* Adjust gap for sm screens and up */
                md:gap-3                     /* Adjust gap for md screens and up */
                lg:gap-3">
                    {isEditingModal ? (
                      // --- Edit Mode Buttons ---
                      <>
                        <button
                          onClick={handleCancelEdit}
                          disabled={isSavingModal}
                          className="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
                          title="Cancel Changes"
                        >
                          <ArrowUturnLeftIcon className="h-5 w-5 mr-2" /> Cancel
                        </button>
                        <button
                          onClick={handleSaveChanges}
                          disabled={isSavingModal}
                          className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
                          title="Save Invoice Changes"
                        >
                          {isSavingModal ? <ArrowPathIcon className="h-5 w-5 mr-2 animate-spin" /> : <CheckIcon className="h-5 w-5 mr-2" />}
                          {isSavingModal ? 'Saving...' : 'Save Changes'}
                        </button>
                      </>
                    ) : (
                      // --- View Mode Buttons (Status Updates) ---
                      <>
                        <button
                          onClick={() => handleStatusUpdate(ANCHOR_STATUS_OPTIONS.APPROVE_FOR_BUYER)}
                          disabled={isLoading || selectedInvoice._doc.status !== 'APPOVAL_PENDING'}
                          className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-500 hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:bg-gray-300 disabled:text-gray-500 disabled:cursor-not-allowed"
                          title="Approve for Buyer Verification"
                        >
                          <CheckCircleIcon className="h-5 w-5 mr-2" /> Approve for Buyer Verification
                        </button>
                        <button
                          onClick={() => handleStatusUpdate(ANCHOR_STATUS_OPTIONS.VERIFY)}
                          disabled={isLoading || selectedInvoice._doc.status === ANCHOR_STATUS_OPTIONS.VERIFY || !['VERIFICATION_PENDING_ANCHOR', 'MORE_INFO_NEEDED_ANCHOR'].includes(selectedInvoice._doc.status)}
                          className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:bg-gray-300 disabled:text-gray-500 disabled:cursor-not-allowed"
                          title="Verify Invoice"
                        >
                          <CheckCircleIcon className="h-5 w-5 mr-2" /> Verify
                        </button>
                        <button
                          onClick={() => handleStatusUpdate(ANCHOR_STATUS_OPTIONS.REJECT)}
                          disabled={isLoading || selectedInvoice._doc.status === ANCHOR_STATUS_OPTIONS.REJECT || !['VERIFICATION_PENDING_ANCHOR', 'MORE_INFO_NEEDED_ANCHOR'].includes(selectedInvoice._doc.status)}
                          className="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:bg-gray-300 disabled:text-gray-500 disabled:cursor-not-allowed"
                          title="Reject Invoice"
                        >
                          <XCircleIcon className="h-5 w-5 mr-2 text-red-500" /> Reject
                        </button>
                        <button
                          onClick={() => handleStatusUpdate(ANCHOR_STATUS_OPTIONS.MORE_INFO)}
                          disabled={isLoading || selectedInvoice._doc.status === ANCHOR_STATUS_OPTIONS.MORE_INFO || !['VERIFICATION_PENDING_ANCHOR'].includes(selectedInvoice._doc.status)} // Can't request more info if already requested
                          className="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:bg-gray-300 disabled:text-gray-500 disabled:cursor-not-allowed"
                          title="Request More Info"
                        >
                          <InformationCircleIcon className="h-5 w-5 mr-2 text-orange-500" /> Need More Info
                        </button>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        {/* --- End Details/Action Modal --- */}

      </div> {/* End Max Width Container */}
    </div> // End Main Page Div
  );
};

export default InvoiceApprovals;